[project]
name = "imgsearch"
version = "0.1.1"
description = "ImgSearch is a lightweight image search engine that supports searching images by image."
readme = "README.md"
authors = [
    { name = "Seamile", email = "<EMAIL>" }
]
classifiers = [
    "Development Status :: 4 - Beta",
    "License :: OSI Approved :: MIT License",
    "Operating System :: MacOS",
    "Operating System :: POSIX :: Linux",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: SCIENCE :: Artificial Intelligence :: Image Recognition",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Utilities",
]
requires-python = ">=3.11"
dependencies = [
    "bidict>=0.23.1",
    "hnswlib>=0.8.0",
    "msgpack>=1.1.1",
    "pillow>=11.3.0",
    "psutil>=7.0.0",
    "pyro5>=5.15",
    # NumPy with version constraints
    "numpy<2; sys_platform=='darwin' and platform_machine=='x86_64'",
    "numpy>=1.24.0; sys_platform!='darwin' or platform_machine!='x86_64'",
    # PyTorch dependencies with platform-specific versions
    "torch==2.2.2; sys_platform=='darwin' and platform_machine=='x86_64'",
    "torch>=2.8.0; sys_platform!='darwin' or platform_machine!='x86_64'",
    "torchvision>=0.17.2",  # dependi: disable-check
    "open-clip-torch @ git+https://github.com/wkcn/TinyCLIP.git@main",
]

[dependency-groups]
dev = [
    "ipython>=9.5.0",
    "pytest>=8.4.2",
]

[project.scripts]
isearch = "imgsearch.client:main"

[build-system]
requires = ["uv_build>=0.8.6,<0.9.0"]
build-backend = "uv_build"
