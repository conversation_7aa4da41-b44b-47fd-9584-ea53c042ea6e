"""
Base unit tests for clip.py module - focusing on core functionality with mocks
"""

import unittest
from unittest.mock import Mock, patch

import torch

from imgsearch.clip import Clip


@patch('open_clip.get_tokenizer', return_value=Mock(name='mock_tokenizer'))
@patch(
    'open_clip.create_model_and_transforms', return_value=(Mock(name='mock_model'), None, Mock(name='mock_processor'))
)
class TestClipBase(unittest.TestCase):
    """Base test class for Clip model wrapper - using mocked dependencies"""

    def test_get_device_explicit(self, mock_create, mock_tokenizer):
        """Test get_device with explicit device name"""
        device = Clip.get_device('cpu')
        self.assertEqual(device.type, 'cpu')

    def test_get_device_cpu_fallback(self, mock_create, mock_tokenizer):
        """Test get_device fallback to CPU"""
        with (
            patch('torch.cuda.is_available', return_value=False),
            patch('torch.backends.mps.is_available', return_value=False),
        ):
            device = Clip.get_device()
            self.assertEqual(device.type, 'cpu')

    def test_init_basic(self, mock_create, mock_tokenizer):
        """Test basic initialization"""
        clip = Clip(device='cpu')
        self.assertEqual(clip.device.type, 'cpu')

    def test_init_default_device(self, mock_create, mock_tokenizer):
        """Test initialization with default device selection"""
        with (
            patch('torch.cuda.is_available', return_value=False),
            patch('torch.backends.mps.is_available', return_value=False),
        ):
            clip = Clip()
            self.assertEqual(clip.device.type, 'cpu')

    def test_init_cuda_device(self, mock_create, mock_tokenizer):
        """Test initialization with CUDA device - skip if not available"""
        with (
            patch('torch.cuda.is_available', return_value=True),
            patch('torch.backends.mps.is_available', return_value=False),
        ):
            clip_1 = Clip()
            self.assertEqual(clip_1.device.type, 'cuda')

            clip_2 = Clip(device='cpu')  # Force CPU to avoid CUDA issues
            self.assertEqual(clip_2.device.type, 'cpu')

    def test_init_mps_device(self, mock_create, mock_tokenizer):
        """Test initialization with MPS device"""
        with (
            patch('torch.cuda.is_available', return_value=False),
            patch('torch.backends.mps.is_available', return_value=True),
        ):
            # Setup mocks for MPS test
            mock_model = Mock()
            mock_model.to.return_value = mock_model
            mock_model.float.return_value = mock_model
            mock_model.eval.return_value = mock_model
            mock_processor = Mock()
            mock_create.return_value = (mock_model, None, mock_processor)

            clip = Clip()
            self.assertEqual(clip.device.type, 'mps')
            mock_model.float.assert_called_once()
            mock_model.eval.assert_called_once()
            mock_model.to.assert_called_once_with(clip.device)

    def test_load_model_mock(self, mock_tokenizer, mock_create):
        """Test model loading with mocks"""
        model, processor, tokenizer = Clip.load_model('test-model')
        mock_create.assert_called_once_with('test-model', pretrained='openai')
        mock_tokenizer.assert_called_once_with('test-model')

        self.assertEqual(model, mock_create.return_value[0])
        self.assertEqual(processor, mock_create.return_value[2])
        self.assertEqual(tokenizer, mock_tokenizer.return_value)

    def test_embed_images_empty_list(self, mock_tokenizer, mock_create):
        """Test embed_images with empty list"""
        clip = Clip(device='cpu')
        result = clip.embed_images([])
        self.assertEqual(result, [])

    def test_torch_set_num_threads(self, mock_tokenizer, mock_create):
        """Test that torch.set_num_threads is called during initialization on CPU"""
        with patch('torch.set_num_threads') as mock_set_num_threads:
            Clip(device='cpu')
            mock_set_num_threads.assert_called_once_with(2)  # or cpu_count()

    @patch('torch.set_num_threads')
    def test_clip_initialization_flow(self, mock_set_threads, mock_tokenizer, mock_create):
        """Test the complete initialization flow"""
        # Setup mocks before Clip initialization
        mock_model = Mock()
        mock_model.to.return_value = mock_model
        mock_model.eval.return_value = mock_model
        mock_model.float.return_value = mock_model
        mock_processor = Mock()
        mock_create.return_value = (mock_model, None, mock_processor)

        clip = Clip(device='cpu')
        mock_set_threads.assert_called_once()

        self.assertEqual(clip.device.type, 'cpu')
        self.assertEqual(clip.processor, mock_processor)
        self.assertEqual(clip.tokenizer, mock_tokenizer.return_value)
        mock_model.to.assert_called_once_with(torch.device('cpu'))
        mock_model.eval.assert_called_once()
